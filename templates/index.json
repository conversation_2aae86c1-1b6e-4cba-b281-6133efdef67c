/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin theme editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "sections": {
    "hero_irjTFL": {
      "type": "hero",
      "blocks": {
        "text_QT3bAX": {
          "type": "text",
          "name": "t:names.heading",
          "settings": {
            "text": "<p>Step Into Personalized Comfort</p>",
            "width": "fit-content",
            "max_width": "normal",
            "alignment": "left",
            "type_preset": "custom",
            "font": "var(--font-body--family)",
            "font_size": "2.5rem",
            "line_height": "normal",
            "letter_spacing": "normal",
            "case": "none",
            "wrap": "balance",
            "color": "var(--color-foreground-heading)",
            "background": false,
            "background_color": "#00000026",
            "corner_radius": 0,
            "padding-block-start": 0,
            "padding-block-end": 0,
            "padding-inline-start": 0,
            "padding-inline-end": 0
          },
          "blocks": {}
        },
        "text_CXHmij": {
          "type": "text",
          "name": "t:names.text",
          "settings": {
            "text": "<p>Custom orthotics made from your foot impression – for all-day support.</p>",
            "width": "fit-content",
            "max_width": "normal",
            "alignment": "left",
            "type_preset": "custom",
            "font": "var(--font-tertiary--family)",
            "font_size": "1.5rem",
            "line_height": "normal",
            "letter_spacing": "normal",
            "case": "none",
            "wrap": "pretty",
            "color": "var(--color-foreground)",
            "background": false,
            "background_color": "#00000026",
            "corner_radius": 0,
            "padding-block-start": 0,
            "padding-block-end": 0,
            "padding-inline-start": 0,
            "padding-inline-end": 0
          },
          "blocks": {}
        },
        "button_3x6Wg7": {
          "type": "button",
          "name": "t:names.button",
          "settings": {
            "label": "Shop now",
            "link": "shopify://collections/all",
            "open_in_new_tab": false,
            "style_class": "button",
            "width": "custom",
            "custom_width": 47,
            "width_mobile": "fit-content",
            "custom_width_mobile": 100
          },
          "blocks": {}
        }
      },
      "block_order": [
        "text_QT3bAX",
        "text_CXHmij",
        "button_3x6Wg7"
      ],
      "name": "Hero",
      "settings": {
        "link": "shopify://collections",
        "open_in_new_tab": false,
        "media_type_1": "video",
        "video_1": "shopify://files/videos/Best_movie_cut_1(1).mp4",
        "media_type_2": "image",
        "content_direction": "column",
        "vertical_on_mobile": true,
        "horizontal_alignment": "flex-start",
        "vertical_alignment": "center",
        "align_baseline": false,
        "horizontal_alignment_flex_direction_column": "center",
        "vertical_alignment_flex_direction_column": "flex-end",
        "gap": 96,
        "section_width": "full-width",
        "section_height": "large",
        "section_height_custom": 50,
        "color_scheme": "scheme-6",
        "toggle_overlay": true,
        "overlay_color": "#0000009e",
        "overlay_style": "gradient",
        "gradient_direction": "to top",
        "blurred_reflection": false,
        "reflection_opacity": 75,
        "padding-block-start": 48,
        "padding-block-end": 48
      }
    },
    "featured_product_mEUYXX": {
      "type": "featured-product",
      "blocks": {
        "media": {
          "type": "_media-without-appearance",
          "name": "t:names.product_media",
          "static": true,
          "settings": {
            "media_type": "image",
            "image": "shopify://shop_images/c047955e094557b5e30d515b.webp",
            "link": "",
            "video_loop": true,
            "video_autoplay": false,
            "image_position": "cover",
            "video_position": "cover"
          },
          "blocks": {}
        },
        "featured-product": {
          "type": "_featured-product",
          "name": "t:names.product",
          "static": true,
          "settings": {},
          "blocks": {
            "featured-product-title": {
              "type": "product-title",
              "name": "t:names.title",
              "static": true,
              "settings": {
                "width": "100%",
                "max_width": "normal",
                "alignment": "left",
                "type_preset": "h5",
                "font": "var(--font-body--family)",
                "font_size": "1rem",
                "line_height": "normal",
                "letter_spacing": "normal",
                "case": "none",
                "wrap": "pretty",
                "color": "var(--color-foreground)",
                "background": false,
                "background_color": "#00000026",
                "corner_radius": 0,
                "padding-block-start": 0,
                "padding-block-end": 0,
                "padding-inline-start": 0,
                "padding-inline-end": 0
              },
              "blocks": {}
            },
            "featured-product-price": {
              "type": "_featured-product-price",
              "name": "t:names.price",
              "static": true,
              "settings": {
                "show_sale_price_first": true,
                "type_preset": ""
              },
              "blocks": {}
            },
            "featured-product-gallery": {
              "type": "_featured-product-gallery",
              "name": "t:names.image",
              "static": true,
              "settings": {
                "image_ratio": "adapt",
                "constrain_to_viewport": "contain"
              },
              "blocks": {}
            },
            "featured-product-swatches": {
              "type": "swatches",
              "name": "t:names.swatches",
              "static": true,
              "settings": {
                "product_swatches_alignment": "flex-start",
                "product_swatches_alignment_mobile": "flex-start",
                "hide_padding": true,
                "product_swatches_padding_top": 4,
                "product_swatches_padding_bottom": 0,
                "product_swatches_padding_left": 0,
                "product_swatches_padding_right": 0
              },
              "blocks": {}
            }
          },
          "block_order": []
        }
      },
      "name": "t:names.featured_product",
      "settings": {
        "product": "on-my-feet-all-day-custom-orthotics",
        "layout": "media-left",
        "color_scheme": "scheme-3",
        "padding-block-start": 0,
        "padding-block-end": 0
      }
    },
    "featured_product_yEF9dK": {
      "type": "featured-product",
      "blocks": {
        "media": {
          "type": "_media-without-appearance",
          "name": "t:names.product_media",
          "disabled": true,
          "static": true,
          "settings": {
            "media_type": "image",
            "link": "",
            "video_loop": true,
            "video_autoplay": false,
            "image_position": "cover",
            "video_position": "cover"
          },
          "blocks": {}
        },
        "featured-product": {
          "type": "_featured-product",
          "name": "t:names.product",
          "static": true,
          "settings": {},
          "blocks": {
            "featured-product-title": {
              "type": "product-title",
              "name": "t:names.title",
              "static": true,
              "settings": {
                "width": "100%",
                "max_width": "normal",
                "alignment": "left",
                "type_preset": "h5",
                "font": "var(--font-body--family)",
                "font_size": "1rem",
                "line_height": "normal",
                "letter_spacing": "normal",
                "case": "none",
                "wrap": "pretty",
                "color": "var(--color-foreground)",
                "background": false,
                "background_color": "#00000026",
                "corner_radius": 0,
                "padding-block-start": 0,
                "padding-block-end": 0,
                "padding-inline-start": 0,
                "padding-inline-end": 0
              },
              "blocks": {}
            },
            "featured-product-price": {
              "type": "_featured-product-price",
              "name": "t:names.price",
              "static": true,
              "settings": {
                "show_sale_price_first": true,
                "type_preset": "paragraph"
              },
              "blocks": {}
            },
            "featured-product-gallery": {
              "type": "_featured-product-gallery",
              "name": "t:names.image",
              "disabled": true,
              "static": true,
              "settings": {
                "image_ratio": "adapt",
                "constrain_to_viewport": "contain"
              },
              "blocks": {}
            },
            "featured-product-swatches": {
              "type": "swatches",
              "name": "t:names.swatches",
              "static": true,
              "settings": {
                "product_swatches_alignment": "flex-start",
                "product_swatches_alignment_mobile": "flex-start",
                "hide_padding": true,
                "product_swatches_padding_top": 4,
                "product_swatches_padding_bottom": 0,
                "product_swatches_padding_left": 0,
                "product_swatches_padding_right": 0
              },
              "blocks": {}
            }
          },
          "block_order": []
        }
      },
      "name": "t:names.featured_product",
      "settings": {
        "product": "on-my-feet-all-day-custom-orthotics",
        "layout": "media-left",
        "color_scheme": "scheme-2",
        "padding-block-start": 0,
        "padding-block-end": 0
      }
    },
    "featured_product_kAzr6f": {
      "type": "featured-product",
      "blocks": {
        "media": {
          "type": "_media-without-appearance",
          "name": "t:names.product_media",
          "static": true,
          "settings": {
            "media_type": "image",
            "link": "",
            "video_loop": true,
            "video_autoplay": false,
            "image_position": "cover",
            "video_position": "cover"
          },
          "blocks": {}
        },
        "featured-product": {
          "type": "_featured-product",
          "name": "t:names.product",
          "static": true,
          "settings": {},
          "blocks": {
            "featured-product-title": {
              "type": "product-title",
              "name": "t:names.title",
              "static": true,
              "settings": {
                "width": "100%",
                "max_width": "normal",
                "alignment": "left",
                "type_preset": "h5",
                "font": "var(--font-body--family)",
                "font_size": "1rem",
                "line_height": "normal",
                "letter_spacing": "normal",
                "case": "none",
                "wrap": "pretty",
                "color": "var(--color-foreground)",
                "background": false,
                "background_color": "#00000026",
                "corner_radius": 0,
                "padding-block-start": 0,
                "padding-block-end": 0,
                "padding-inline-start": 0,
                "padding-inline-end": 0
              },
              "blocks": {}
            },
            "featured-product-price": {
              "type": "_featured-product-price",
              "name": "t:names.price",
              "static": true,
              "settings": {
                "show_sale_price_first": true,
                "type_preset": "paragraph"
              },
              "blocks": {}
            },
            "featured-product-gallery": {
              "type": "_featured-product-gallery",
              "name": "t:names.image",
              "static": true,
              "settings": {
                "image_ratio": "adapt",
                "constrain_to_viewport": "contain"
              },
              "blocks": {}
            },
            "featured-product-swatches": {
              "type": "swatches",
              "name": "t:names.swatches",
              "static": true,
              "settings": {
                "product_swatches_alignment": "flex-start",
                "product_swatches_alignment_mobile": "flex-start",
                "hide_padding": true,
                "product_swatches_padding_top": 4,
                "product_swatches_padding_bottom": 0,
                "product_swatches_padding_left": 0,
                "product_swatches_padding_right": 0
              },
              "blocks": {}
            }
          },
          "block_order": []
        }
      },
      "name": "t:names.featured_product",
      "settings": {
        "product": "",
        "layout": "media-right",
        "color_scheme": "scheme-2",
        "padding-block-start": 0,
        "padding-block-end": 0
      }
    },
    "product_list_nQbyNK": {
      "type": "product-list",
      "blocks": {
        "static-header": {
          "type": "_product-list-content",
          "static": true,
          "settings": {
            "content_direction": "column",
            "vertical_on_mobile": true,
            "horizontal_alignment": "flex-start",
            "vertical_alignment": "center",
            "align_baseline": false,
            "horizontal_alignment_flex_direction_column": "center",
            "vertical_alignment_flex_direction_column": "center",
            "gap": 12,
            "width": "fill",
            "custom_width": 100,
            "width_mobile": "fill",
            "custom_width_mobile": 100,
            "height": "fit",
            "custom_height": 100,
            "inherit_color_scheme": true,
            "color_scheme": "",
            "background_media": "none",
            "video_position": "cover",
            "background_image_position": "cover",
            "border": "none",
            "border_width": 1,
            "border_opacity": 100,
            "border_radius": 0,
            "padding-block-start": 0,
            "padding-block-end": 0,
            "padding-inline-start": 0,
            "padding-inline-end": 0
          },
          "blocks": {
            "product_list_text_3FXyz8": {
              "type": "_product-list-text",
              "name": "t:names.collection_title",
              "settings": {
                "text": "<h5>Functional everyday essentials</h5>",
                "width": "fit-content",
                "max_width": "normal",
                "alignment": "left",
                "type_preset": "h3",
                "font": "var(--font-tertiary--family)",
                "font_size": "var(--font-size--heading-md)",
                "line_height": "normal",
                "letter_spacing": "normal",
                "case": "none",
                "wrap": "pretty",
                "color": "",
                "background": false,
                "background_color": "#00000026",
                "corner_radius": 0,
                "padding-block-start": 0,
                "padding-block-end": 0,
                "padding-inline-start": 0,
                "padding-inline-end": 0
              },
              "blocks": {}
            }
          },
          "block_order": [
            "product_list_text_3FXyz8"
          ]
        },
        "static-product-card": {
          "type": "_product-card",
          "name": "t:names.product_card",
          "static": true,
          "settings": {
            "product_card_gap": 20,
            "inherit_color_scheme": true,
            "color_scheme": "",
            "border": "none",
            "border_width": 1,
            "border_opacity": 100,
            "border_radius": 0,
            "padding-block-start": 0,
            "padding-block-end": 0,
            "padding-inline-start": 0,
            "padding-inline-end": 0
          },
          "blocks": {
            "product_card_gallery_FntBtk": {
              "type": "_product-card-gallery",
              "name": "t:names.product_card_media",
              "settings": {
                "image_ratio": "portrait",
                "border": "none",
                "border_width": 1,
                "border_opacity": 100,
                "border_radius": 0,
                "padding-block-start": 0,
                "padding-block-end": 0,
                "padding-inline-start": 0,
                "padding-inline-end": 0
              },
              "blocks": {}
            },
            "group_pVBb3Q": {
              "type": "_product-card-group",
              "name": "t:names.group",
              "settings": {
                "link": "",
                "open_in_new_tab": false,
                "content_direction": "column",
                "vertical_on_mobile": true,
                "horizontal_alignment": "flex-start",
                "vertical_alignment": "center",
                "align_baseline": false,
                "horizontal_alignment_flex_direction_column": "center",
                "vertical_alignment_flex_direction_column": "center",
                "gap": 4,
                "width": "fill",
                "custom_width": 100,
                "width_mobile": "fill",
                "custom_width_mobile": 100,
                "height": "fit",
                "custom_height": 100,
                "inherit_color_scheme": true,
                "color_scheme": "",
                "background_media": "none",
                "video_position": "cover",
                "background_image_position": "cover",
                "border": "none",
                "border_width": 1,
                "border_opacity": 100,
                "border_radius": 0,
                "toggle_overlay": false,
                "overlay_color": "#00000026",
                "overlay_style": "solid",
                "gradient_direction": "to top",
                "padding-block-start": 0,
                "padding-block-end": 0,
                "padding-inline-start": 0,
                "padding-inline-end": 0
              },
              "blocks": {
                "product_title_AWMHkm": {
                  "type": "product-title",
                  "name": "t:names.product_title",
                  "settings": {
                    "width": "fit-content",
                    "max_width": "normal",
                    "alignment": "left",
                    "type_preset": "h5",
                    "font": "var(--font-body--family)",
                    "font_size": "1rem",
                    "line_height": "normal",
                    "letter_spacing": "normal",
                    "case": "none",
                    "wrap": "pretty",
                    "color": "var(--color-foreground)",
                    "background": false,
                    "background_color": "#00000026",
                    "corner_radius": 0,
                    "padding-block-start": 0,
                    "padding-block-end": 0,
                    "padding-inline-start": 0,
                    "padding-inline-end": 0
                  },
                  "blocks": {}
                },
                "price_tcappF": {
                  "type": "price",
                  "name": "t:names.product_price",
                  "settings": {
                    "show_sale_price_first": true,
                    "show_installments": false,
                    "show_tax_info": false,
                    "type_preset": "paragraph",
                    "width": "100%",
                    "alignment": "center",
                    "font": "var(--font-body--family)",
                    "font_size": "1rem",
                    "line_height": "normal",
                    "letter_spacing": "normal",
                    "case": "none",
                    "color": "var(--color-foreground)",
                    "padding-block-start": 0,
                    "padding-block-end": 0,
                    "padding-inline-start": 0,
                    "padding-inline-end": 0
                  },
                  "blocks": {}
                }
              },
              "block_order": [
                "product_title_AWMHkm",
                "price_tcappF"
              ]
            }
          },
          "block_order": [
            "product_card_gallery_FntBtk",
            "group_pVBb3Q"
          ]
        }
      },
      "name": "t:names.products_grid",
      "settings": {
        "collection": "all",
        "layout_type": "grid",
        "carousel_on_mobile": false,
        "max_products": 4,
        "columns": 4,
        "mobile_columns": "2",
        "columns_gap": 24,
        "rows_gap": 12,
        "icons_style": "arrow",
        "icons_shape": "none",
        "section_width": "page-width",
        "horizontal_alignment": "flex-start",
        "gap": 28,
        "color_scheme": "scheme-1",
        "padding-block-start": 48,
        "padding-block-end": 48
      }
    },
    "collection_list_bGtkjG": {
      "type": "collection-list",
      "blocks": {
        "static-collection-card": {
          "type": "_collection-card",
          "name": "t:names.collection_card",
          "static": true,
          "settings": {
            "placement": "on_image",
            "horizontal_alignment": "flex-start",
            "vertical_alignment": "flex-end",
            "collection_card_gap": 8,
            "inherit_color_scheme": false,
            "color_scheme": "scheme-6",
            "border": "none",
            "border_width": 1,
            "border_opacity": 100,
            "border_radius": 0
          },
          "blocks": {
            "collection_title_YE93WK": {
              "type": "collection-title",
              "name": "t:names.collection_title",
              "settings": {
                "width": "fit-content",
                "max_width": "normal",
                "alignment": "left",
                "type_preset": "h5",
                "font": "var(--font-body--family)",
                "font_size": "1rem",
                "line_height": "normal",
                "letter_spacing": "normal",
                "case": "none",
                "wrap": "pretty",
                "color": "var(--color-foreground)",
                "background": false,
                "background_color": "#00000026",
                "corner_radius": 0,
                "padding-block-start": 4,
                "padding-block-end": 4,
                "padding-inline-start": 8,
                "padding-inline-end": 8
              },
              "blocks": {}
            },
            "text_fqzjqy": {
              "type": "text",
              "name": "t:names.text",
              "settings": {
                "text": "<p>{{ closest.collection.title }}</p>",
                "width": "fit-content",
                "max_width": "normal",
                "alignment": "left",
                "type_preset": "h5",
                "font": "var(--font-body--family)",
                "font_size": "",
                "line_height": "normal",
                "letter_spacing": "normal",
                "case": "none",
                "wrap": "pretty",
                "color": "var(--color-foreground)",
                "background": false,
                "background_color": "#ffffff",
                "corner_radius": 0,
                "padding-block-start": 4,
                "padding-block-end": 4,
                "padding-inline-start": 8,
                "padding-inline-end": 8
              },
              "blocks": {}
            },
            "collection-card-image": {
              "type": "_collection-card-image",
              "name": "t:names.collection_card_image",
              "static": true,
              "settings": {
                "image_ratio": "portrait",
                "toggle_overlay": false,
                "overlay_color": "#00000026",
                "overlay_style": "solid",
                "gradient_direction": "to top",
                "border": "none",
                "border_width": 1,
                "border_opacity": 100,
                "border_radius": 0
              },
              "blocks": {}
            }
          },
          "block_order": [
            "collection_title_YE93WK",
            "text_fqzjqy"
          ]
        }
      },
      "name": "t:names.collections_grid",
      "settings": {
        "collection_list": [],
        "layout_type": "grid",
        "carousel_on_mobile": true,
        "columns": 3,
        "mobile_columns": "1",
        "columns_gap": 0,
        "bento_gap": 8,
        "rows_gap": 0,
        "max_collections": 4,
        "icons_style": "arrow",
        "icons_shape": "none",
        "section_width": "full-width",
        "gap": 0,
        "color_scheme": "",
        "padding-block-start": 48,
        "padding-block-end": 48
      }
    }
  },
  "order": [
    "hero_irjTFL",
    "featured_product_mEUYXX",
    "featured_product_yEF9dK",
    "featured_product_kAzr6f",
    "product_list_nQbyNK",
    "collection_list_bGtkjG"
  ]
}
