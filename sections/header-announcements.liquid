

<div class="section-background color-{{ section.settings.color_scheme }}"></div>
<aside
  class="announcement-bar spacing-style section section--{{ section.settings.section_width }} color-{{ section.settings.color_scheme }}"
  style="{% render 'spacing-padding', settings: section.settings %}; --border-bottom-width: {{ section.settings.divider_width }}px;"
>
  <div class="announcement-bar__content">
    <div class="announcement-bar__static">
      Bring Nature Indoors
    </div>
    <div class="announcement-bar__scrolling" id="scrolling-announcements">
      {% for block in section.blocks %}
        <div class="announcement-bar__slide" data-index="{{ forloop.index0 }}">
          {% if block.settings.link != blank %}
            <a href="{{ block.settings.link }}" {% if block.settings.open_in_new_tab %}target="_blank"{% endif %}>
              {{ block.settings.text }}
            </a>
          {% else %}
            {{ block.settings.text }}
          {% endif %}
        </div>
      {% endfor %}
    </div>
  </div>
</aside>

<script>
document.addEventListener('DOMContentLoaded', function() {
  const container = document.getElementById('scrolling-announcements');
  if (!container) {
    console.log('Container not found');
    return;
  }

  const slides = container.querySelectorAll('.announcement-bar__slide');
  console.log('Found slides:', slides.length);
  let currentIndex = 0;

  if (slides.length > 1) {
    // Show first slide
    slides[0].classList.add('active');
    console.log('First slide activated');

    function nextSlide() {
      console.log('Switching from slide', currentIndex, 'to', (currentIndex + 1) % slides.length);

      // Hide current slide
      slides[currentIndex].classList.remove('active');
      slides[currentIndex].classList.add('exit');

      // Move to next slide
      currentIndex = (currentIndex + 1) % slides.length;

      // Show next slide after a short delay
      setTimeout(() => {
        slides.forEach(slide => slide.classList.remove('exit'));
        slides[currentIndex].classList.add('active');
      }, 300);
    }

    // Auto-advance every 2 seconds
    setInterval(nextSlide, 2000);
  } else if (slides.length === 1) {
    slides[0].classList.add('active');
    console.log('Single slide activated');
  }
});
</script>

{% stylesheet %}
  .announcement-bar {
    border-block-end: var(--border-bottom-width) solid var(--color-border);
    background: #4a7c59 !important; /* EasyPlant green color */
    padding: 12px 0;
  }

  .announcement-bar .color-scheme-2 {
    background: #4a7c59 !important;
    color: white !important;
  }

  .announcement-bar__content {
    text-align: center;
    color: white;
    font-weight: 500;
  }

  .announcement-bar__static {
    font-size: 0.875rem;
    margin-bottom: 4px;
    opacity: 0.9;
    color: white !important;
    font-weight: 500;
  }

  .announcement-bar__scrolling {
    font-size: 0.875rem;
    font-weight: 600;
    height: 20px;
    overflow: hidden;
    position: relative;
  }

  .announcement-bar__slide {
    position: absolute;
    width: 100%;
    top: 0;
    left: 0;
    color: white !important;
    font-size: 0.875rem;
    font-weight: 600;
    text-align: center;
    transition: transform 0.6s ease-in-out, opacity 0.6s ease-in-out;
    transform: translateY(20px);
    opacity: 0;
  }

  .announcement-bar__slide.active {
    transform: translateY(0);
    opacity: 1;
  }

  .announcement-bar__slide.exit {
    transform: translateY(-20px);
    opacity: 0;
  }

  .announcement-bar__slide a {
    color: white !important;
    text-decoration: none;
  }

  .announcement-bar__slide a:hover {
    color: rgba(255, 255, 255, 0.8) !important;
  }






{% endstylesheet %}

{% schema %}
{
  "name": "t:names.announcement_bar",
  "blocks": [
    {
      "type": "_announcement"
    }
  ],
  "enabled_on": {
    "groups": ["header"]
  },
  "settings": [
    {
      "type": "range",
      "id": "speed",
      "label": "t:settings.speed",
      "min": 2,
      "max": 10,
      "default": 5,
      "unit": "sec"
    },
    {
      "type": "header",
      "content": "t:content.appearance"
    },
    {
      "type": "select",
      "id": "section_width",
      "label": "t:settings.section_width",
      "options": [
        {
          "value": "page-width",
          "label": "t:options.page"
        },
        {
          "value": "full-width",
          "label": "t:options.full"
        }
      ],
      "default": "page-width"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "default": "scheme-4",
      "label": "t:settings.color_scheme"
    },
    {
      "type": "range",
      "id": "divider_width",
      "label": "t:settings.divider_thickness",
      "min": 0,
      "max": 5,
      "step": 0.5,
      "unit": "px",
      "default": 0
    },
    {
      "type": "header",
      "content": "t:content.padding"
    },
    {
      "type": "range",
      "id": "padding-block-start",
      "label": "t:settings.top",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "default": 15
    },
    {
      "type": "range",
      "id": "padding-block-end",
      "label": "t:settings.bottom",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "default": 15
    }
  ],
  "presets": [
    {
      "name": "t:names.announcement_bar",
      "blocks": {
        "announcement_1": {
          "type": "_announcement"
        }
      },
      "block_order": ["announcement_1"]
    }
  ]
}
{% endschema %}
