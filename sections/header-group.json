/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin theme editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "type": "header",
  "name": "t:names.header",
  "sections": {
    "promo_banner_section": {
      "type": "header-announcements",
      "blocks": {
        "announcement_1": {
          "type": "announcement",
          "settings": {
            "text": "100% FROM HOME! 180-day money-back*",
            "link": "",
            "open_in_new_tab": false
          }
        },
        "announcement_2": {
          "type": "announcement",
          "settings": {
            "text": "FSA/ HSA Eligible! Use your funds before they expire",
            "link": "",
            "open_in_new_tab": false
          }
        },
        "announcement_3": {
          "type": "announcement",
          "settings": {
            "text": "Up to 70% OFF | FLASH SALE",
            "link": "",
            "open_in_new_tab": false
          }
        }
      },
      "block_order": [
        "announcement_1",
        "announcement_2",
        "announcement_3"
      ],
      "settings": {
        "static_text": "Bring Nature Indoors",
        "background_color": "#4a7c59",
        "text_color": "#ffffff",
        "static_text_color": "#ffffff",
        "speed": 4,
        "section_width": "full-width",
        "color_scheme": "scheme-2",
        "divider_width": 0,
        "padding-block-start": 8,
        "padding-block-end": 8
      }
    },
    "header_section": {
      "type": "header",
      "blocks": {
        "header-logo": {
          "type": "_header-logo",
          "static": true,
          "settings": {
            "hide_logo_on_home_page": false,
            "custom_height": 40,
            "custom_height_mobile": 40,
            "padding-block-start": 0,
            "padding-block-end": 0,
            "padding-inline-start": 0,
            "padding-inline-end": 0
          },
          "blocks": {}
        },
        "header-menu": {
          "type": "_header-menu",
          "static": true,
          "settings": {
            "menu": "main-menu",
            "menu_style": "featured_products",
            "featured_products_aspect_ratio": "4 / 5",
            "featured_collections_aspect_ratio": "16 / 9",
            "image_border_radius": 0,
            "product_title_case": "uppercase",
            "collection_title_case": "default",
            "color_scheme": "",
            "menu_font_style": "inverse",
            "type_font_primary_link": "subheading",
            "type_font_primary_size": "var(--font-size--body-md)",
            "type_case_primary_link": "none",
            "type_font_secondary_link": "secondary",
            "type_case_secondary_link": "none",
            "type_font_tertiary_link": "secondary",
            "type_case_tertiary_link": "none",
            "navigation_bar": false,
            "color_scheme_navigation_bar": "",
            "drawer_accordion": false,
            "drawer_accordion_expand_first": false,
            "drawer_dividers": false
          },
          "blocks": {}
        }
      },
      "settings": {
        "logo_position": "left",
        "menu_position": "center",
        "menu_row": "top",
        "show_search": false,
        "search_position": "right",
        "search_row": "top",
        "show_country": false,
        "country_selector_style": false,
        "show_language": false,
        "localization_font": "heading",
        "localization_font_size": "1rem",
        "localization_position": "right",
        "localization_row": "top",
        "section_width": "page-width",
        "section_height": "standard",
        "enable_sticky_header": "scroll-up",
        "divider_width": 0,
        "divider_size": "page-width",
        "border_width": 0,
        "color_scheme_top": "",
        "color_scheme_bottom": "",
        "color_scheme_transparent": "scheme-6",
        "enable_transparent_header_home": true,
        "home_color_scheme": "inverse",
        "enable_transparent_header_product": false,
        "product_color_scheme": "default",
        "enable_transparent_header_collection": false,
        "collection_color_scheme": "default"
      }
    }
  },
  "order": [
    "promo_banner_section",
    "header_section"
  ]
}
